import { Perspective<PERSON><PERSON>ra } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";
import { Suspense } from "react";
import CanvesLoader from "./CanvesLoader";

const Hero = () => {
  return (
    <section className="min-h-screen w-full flex flex-col relative">
      <div className="w-full max-auto flex flex-col sm:mt-36 mt-20 c-space gap-3">
        <p className="sm:text-3xl text-2xl font-medium text-white text-center font-generalsans">
          Hi, I'm <PERSON>er <span className="waving-hand">👋</span>
          <p className="hero_tag text-gray_gradient e">Building Brand and Products</p>
        </p>
      </div>

      <div className="w-full h-full absolute inset-0">
        <Canvas className="w-full h-full">
          <Suspense fallback={<CanvesLoader />}>
            <PerspectiveCamera makeDefault position={[0, 0, 30]} />
            
          </Suspense>
        </Canvas>
      </div>
    </section>
  );
};

export default Hero;
